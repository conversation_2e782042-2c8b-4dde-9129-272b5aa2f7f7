{"$schema": "https://inlang.com/schema/project-settings", "modules": ["https://cdn.jsdelivr.net/npm/@inlang/plugin-message-format@4/dist/index.js", "https://cdn.jsdelivr.net/npm/@inlang/plugin-m-function-matcher@2/dist/index.js"], "plugin.inlang.messageFormat": {"pathPattern": "./messages/{locale}.json"}, "baseLocale": "en", "locales": ["en", "es", "fa", "ar", "tr", "nl", "fr"], "strategy": ["url", "preferredLanguage", "cookie", "baseLocale"], "urlPatterns": [{"pattern": ":protocol://:domain(.*)::port?/:path(.*)?", "localized": [["es", ":protocol://:domain(.*)::port?/es/:path(.*)?"], ["fa", ":protocol://:domain(.*)::port?/fa/:path(.*)?"], ["ar", ":protocol://:domain(.*)::port?/ar/:path(.*)?"], ["tr", ":protocol://:domain(.*)::port?/tr/:path(.*)?"], ["nl", ":protocol://:domain(.*)::port?/nl/:path(.*)?"], ["fr", ":protocol://:domain(.*)::port?/fr/:path(.*)?"], ["en", ":protocol://:domain(.*)::port?/:path(.*)?"]]}]}