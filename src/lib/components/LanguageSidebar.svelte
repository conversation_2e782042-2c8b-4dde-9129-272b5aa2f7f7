<script lang="ts">
  import { getLocale, localizeHref, type Locale } from '$lib/paraglide/runtime';
  import { m } from '$lib/paraglide/messages.js';
  import { page } from '$app/state';
  import { goto } from '$app/navigation';

  interface Language {
    code: Locale;
    name: string;
    direction: 'ltr' | 'rtl';
  }

  const languages: Language[] = [
  { code: 'en', name: m.language_english(), direction: 'ltr' },
  { code: 'fa', name: m.language_persian(), direction: 'rtl' },
  { code: 'ar', name: m.language_arabic(), direction: 'rtl' },
  { code: 'tr', name: m.language_turkish(), direction: 'ltr' },
  { code: 'fr', name: m.language_french(), direction: 'ltr' },
  { code: 'es', name: m.language_spanish(), direction: 'ltr' }
  ];

  let isOpen = false;
  $: currentLocale = getLocale();

  function handleLanguageChange(langCode: Locale) {
    // Get the current page URL and localize it for the new locale
    const currentPath = page.url.pathname + page.url.search + page.url.hash;
    const localizedPath = localizeHref(currentPath, { locale: langCode });

    // Navigate to the localized URL
    goto(localizedPath);
    isOpen = false;
  }

  function toggleSidebar() {
    isOpen = !isOpen;
  }
</script>

<!-- Language Toggle Button -->
<button
class="fixed top-6 left-6 z-50 bg-white/90 backdrop-blur-sm rounded-full p-3 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
onclick={toggleSidebar}
aria-label="Toggle language menu"
>
<svg
class="w-6 h-6 text-emerald-700"
fill="none"
stroke="currentColor"
viewBox="0 0 24 24"
xmlns="http://www.w3.org/2000/svg"
>
<path
stroke-linecap="round"
stroke-linejoin="round"
stroke-width="2"
d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"
></path>
</svg>
</button>

<!-- Sidebar Overlay -->
{#if isOpen}
<div
class="fixed inset-0 bg-black/20 backdrop-blur-sm z-40 transition-opacity duration-300"
onclick={toggleSidebar}
role="button"
tabindex="0"
onkeydown={(e) => e.key === 'Escape' && toggleSidebar()}
></div>
{/if}

<!-- Language Sidebar -->
<aside
class="fixed top-0 left-0 h-full w-80 bg-white/95 backdrop-blur-md shadow-2xl z-50 transform transition-transform duration-300 ease-in-out {isOpen
? 'translate-x-0'
: '-translate-x-full'}"
>
<div class="p-6">
  <div class="flex items-center justify-between mb-8">
    <h2 class="text-xl font-bold text-emerald-800">Languages</h2>
    <button
    onclick={toggleSidebar}
    class="p-2 hover:bg-emerald-100 rounded-full transition-colors duration-200"
    aria-label="Close language menu"
    >
    <svg
    class="w-5 h-5 text-emerald-700"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
    >
    <path
    stroke-linecap="round"
    stroke-linejoin="round"
    stroke-width="2"
    d="M6 18L18 6M6 6l12 12"
    ></path>
  </svg>
</button>
</div>

<nav class="space-y-2">
  {#each languages as language}
  <button
  class="w-full text-left p-4 rounded-lg transition-all duration-200 hover:bg-emerald-50 hover:shadow-md group {currentLocale ===
  language.code
  ? 'bg-emerald-100 shadow-md border-l-4 border-emerald-500'
  : 'hover:translate-x-1'}"
  onclick={() => handleLanguageChange(language.code)}
  dir={language.direction}
  >
  <div class="flex items-center justify-between">
    <span
    class="text-lg font-medium text-emerald-800 group-hover:text-emerald-900 {language.direction ===
    'rtl'
    ? 'font-arabic'
    : ''}"
    >
    {language.name}
  </span>
  {#if currentLocale === language.code}
  <svg
  class="w-5 h-5 text-emerald-600"
  fill="currentColor"
  viewBox="0 0 20 20"
  >
  <path
  fill-rule="evenodd"
  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
  clip-rule="evenodd"
  ></path>
</svg>
{/if}
</div>
</button>
{/each}
</nav>
</div>
</aside>

<style>
  .font-arabic {
    font-family: 'Noto Sans Arabic', 'Arial Unicode MS', sans-serif;
  }
</style>
